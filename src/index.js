// <PERSON>f<PERSON>e Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)
// Includes client-side pagination for a page-by-page reading experience.

// Display Configuration - Set to true/false to show/hide elements
var showVerseNumbers = false;      // <sup class="versenum">
var showCrossReferences = false;   // <sup class="crossreference">
var showFootnotes = false;         // <sup class="footnote">
var showSectionHeadings = false;   // <h3> section titles

const BIBLE_BOOKS = [
    { name: 'Genesis', chapters: 50 },
    { name: 'Exodus', chapters: 40 },
    { name: '<PERSON><PERSON>', chapters: 27 },
    { name: 'Numbers', chapters: 36 },
    { name: 'Deuteronomy', chapters: 34 },
    { name: '<PERSON>', chapters: 24 },
    { name: 'Judges', chapters: 21 },
    { name: '<PERSON>', chapters: 4 },
    { name: '1 <PERSON>', chapters: 31 },
    { name: '2 <PERSON>', chapters: 24 },
    { name: '1 <PERSON>', chapters: 22 },
    { name: '2 <PERSON>', chapters: 25 },
    { name: '1 Chronicles', chapters: 29 },
    { name: '2 Chronicles', chapters: 36 },
    { name: '<PERSON>', chapters: 10 },
    { name: 'Neh<PERSON><PERSON>', chapters: 13 },
    { name: '<PERSON>', chapters: 10 },
    { name: 'Job', chapters: 42 },
    { name: 'Psalms', chapters: 150 },
    { name: 'Proverbs', chapters: 31 },
    { name: 'Ecclesiastes', chapters: 12 },
    { name: 'Song of Songs', chapters: 8 },
    { name: 'Isaiah', chapters: 66 },
    { name: '<PERSON>', chapters: 52 },
    { name: 'Lamentations', chapters: 5 },
    { name: 'Ezekiel', chapters: 48 },
    { name: 'Daniel', chapters: 12 },
    { name: 'Hosea', chapters: 14 },
    { name: 'Joel', chapters: 3 },
    { name: 'Amos', chapters: 9 },
    { name: 'Obadia', chapters: 1 },
    { name: 'Jonah', chapters: 4 },
    { name: 'Micah', chapters: 7 },
    { name: 'Nahum', chapters: 3 },
    { name: 'Habakkuk', chapters: 3 },
    { name: 'Zephaniah', chapters: 3 },
    { name: 'Haggai', chapters: 2 },
    { name: 'Zechariah', chapters: 14 },
    { name: 'Malachi', chapters: 4 },
    { name: 'Matthew', chapters: 28 },
    { name: 'Mark', chapters: 16 },
    { name: 'Luke', chapters: 24 },
    { name: 'John', chapters: 21 },
    { name: 'Acts', chapters: 28 },
    { name: 'Romans', chapters: 16 },
    { name: '1 Corinthians', chapters: 16 },
    { name: '2 Corinthians', chapters: 13 },
    { name: 'Galatians', chapters: 6 },
    { name: 'Ephesians', chapters: 6 },
    { name: 'Philippians', chapters: 4 },
    { name: 'Colossians', chapters: 4 },
    { name: '1 Thessalonians', chapters: 5 },
    { name: '2 Thessalonians', chapters: 3 },
    { name: '1 Timothy', chapters: 6 },
    { name: '2 Timothy', chapters: 4 },
    { name: 'Titus', chapters: 3 },
    { name: 'Philemon', chapters: 1 },
    { name: 'Hebrews', chapters: 13 },
    { name: 'James', chapters: 5 },
    { name: '1 Peter', chapters: 5 },
    { name: '2 Peter', chapters: 3 },
    { name: '1 John', chapters: 5 },
    { name: '2 John', chapters: 1 },
    { name: '3 John', chapters: 1 },
    { name: 'Jude', chapters: 1 },
    { name: 'Revelation', chapters: 22 }
];

function parsePassageReference(passageRef) {
    var match = passageRef.match(/^(.+?)\s+(\d+)$/);
    if (match) {
        return {
            book: match[1].trim(),
            chapter: parseInt(match[2])
        };
    }
    return null;
}

function getNavigationUrls(passageRef, translation) {
    var parsed = parsePassageReference(passageRef);
    if (!parsed) return { prev: null, next: null };
    
    var currentBookIndex = -1;
    for (var i = 0; i < BIBLE_BOOKS.length; i++) {
        if (BIBLE_BOOKS[i].name.toLowerCase() === parsed.book.toLowerCase()) {
            currentBookIndex = i;
            break;
        }
    }
    if (currentBookIndex === -1) return { prev: null, next: null };
    
    var currentBook = BIBLE_BOOKS[currentBookIndex];
    var currentChapter = parsed.chapter;
    
    var prevUrl = null;
    var nextUrl = null;
    
    if (currentChapter > 1) {
        prevUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter - 1)) + '&version=' + translation;
    } else if (currentBookIndex > 0) {
        var prevBook = BIBLE_BOOKS[currentBookIndex - 1];
        prevUrl = '/?search=' + encodeURIComponent(prevBook.name + ' ' + prevBook.chapters) + '&version=' + translation;
    }
    
    if (currentChapter < currentBook.chapters) {
        nextUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter + 1)) + '&version=' + translation;
    } else if (currentBookIndex < BIBLE_BOOKS.length - 1) {
        var nextBook = BIBLE_BOOKS[currentBookIndex + 1];
        nextUrl = '/?search=' + encodeURIComponent(nextBook.name + ' 1') + '&version=' + translation;
    }
    
    return { prev: prevUrl, next: nextUrl };
}

function generateDisplayCSS() {
    var css = '';
    if (!showVerseNumbers) css += '        .versenum { display: none; }\n';
    if (!showCrossReferences) css += '        .crossreference { display: none; }\n';
    if (!showFootnotes) css += '        .footnote { display: none; }\n';
    if (!showSectionHeadings) css += '        h3 { display: none; }\n';
    css += '        .chapternum { display: none; }\n';
    css += '        .woj { color: inherit; font-weight: inherit; }\n';
    return css;
}

export default {
    async fetch(request) {
        try {
            var url = new URL(request.url);
            if (url.pathname === '/favicon.ico') {
                return new Response(null, { status: 204 });
            }

            var searchParam = url.searchParams.get('search');
            var versionParam = url.searchParams.get('version');

            if (!searchParam || !versionParam) {
                return new Response('Invalid URL. Use: /?search=John%205&version=NIV', { status: 400 });
            }

            var translation = versionParam.toUpperCase();
            var passageRef = decodeURIComponent(searchParam);
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            var response = await fetch(bibleGatewayUrl, {
                headers: { 'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0' }
            });

            if (!response.ok) {
                return new Response('Failed to fetch passage: ' + response.status, { status: 500 });
            }

            var htmlText = await response.text();
            var stdTextStart = htmlText.search(/<div[^>]*class=['"]std-text['"][^>]*>/);
            var contentContainer = '';
            var contentStart = -1;
            var contentEnd = -1;

            if (stdTextStart !== -1) {
                var openTagMatch = htmlText.substring(stdTextStart).match(/^<div[^>]*class=['"]std-text['"][^>]*>/);
                if (!openTagMatch) { return new Response('Could not parse std-text div tag.', { status: 404 }); }
                contentStart = stdTextStart + openTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) { return new Response('Could not find complete std-text content.', { status: 404 }); }
                contentContainer = htmlText.substring(stdTextStart, contentEnd + 6);
            } else {
                var versionDivPattern = new RegExp('<div[^>]*class="[^"]*version-' + translation + '[^"]*"[^>]*>');
                var versionDivStart = htmlText.search(versionDivPattern);
                if (versionDivStart === -1) { return new Response('Could not find passage content.', { status: 404 }); }
                var versionOpenTagMatch = htmlText.substring(versionDivStart).match(/^<div[^>]*>/);
                if (!versionOpenTagMatch) { return new Response('Could not parse version div tag.', { status: 404 }); }
                contentStart = versionDivStart + versionOpenTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) { return new Response('Could not find complete version content.', { status: 404 }); }
                var rawContent = htmlText.substring(contentStart, contentEnd);
                rawContent = rawContent.replace(/<h1[^>]*class=['"][^'"]*passage-display[^'"]*['"][^>]*>[\s\S]*?<\/h1>/g, '');
                rawContent = rawContent.replace(/<div[^>]*class=['"][^'"]*dropdowns[^'"]*['"][^>]*>[\s\S]*?<\/div>/g, '');
                contentContainer = '<div class="std-text">' + rawContent + '</div>';
            }

            var titleMatch = htmlText.match(/<h1[^>]*class=['"][^'"]*bcv[^'"]*['"][^>]*>([\s\S]*?)<\/h1>/);
            var title = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : '';
            var cleanTitle = title || passageRef;
            if (cleanTitle.indexOf(' - ') !== -1) {
                cleanTitle = cleanTitle.substring(0, cleanTitle.indexOf(' - '));
            }

            var navigation = getNavigationUrls(passageRef, translation);

            var clientSideScript = `
<script>
    // ES5-compatible client-side pagination script
    var pages = [];
    var currentPage = 0;
    var totalPages = 0;

    window.onload = function() {
        var sourceEl = document.getElementById('source-content');
        var viewportEl = document.getElementById('pagination-viewport');
        var prevButton = document.getElementById('prev-button');
        var nextButton = document.getElementById('next-button');
        var indicatorEl = document.getElementById('page-indicator');

        if (!sourceEl || !sourceEl.hasChildNodes()) {
            indicatorEl.innerHTML = 'No content found.';
            return;
        }

        paginateContent(sourceEl, viewportEl);
        showPage(0);

        prevButton.onclick = handlePrevClick;
        nextButton.onclick = handleNextClick;
    };

    function paginateContent(sourceElement, viewportElement) {
        var navEl = document.getElementById('navigation-container');
        var topOffset = viewportElement.offsetTop;
        var bottomOffset = navEl.offsetTop;
        var viewportHeight = bottomOffset - topOffset - 20; // 20px buffer

        var tempPage = document.createElement('div');
        tempPage.style.visibility = 'hidden'; // Render off-screen to measure
        document.body.appendChild(tempPage);

        var nodes = Array.prototype.slice.call(sourceElement.childNodes);

        while (nodes.length > 0) {
            var currentNode = nodes.shift();
            tempPage.appendChild(currentNode);

            if (tempPage.offsetHeight > viewportHeight) {
                tempPage.removeChild(currentNode);
                pages.push(tempPage.innerHTML);
                tempPage.innerHTML = '';
                tempPage.appendChild(currentNode);
            }
        }

        if (tempPage.hasChildNodes()) {
            pages.push(tempPage.innerHTML);
        }
        
        document.body.removeChild(tempPage);
        totalPages = pages.length;
    }

    function showPage(pageIndex) {
        currentPage = pageIndex;
        var viewportEl = document.getElementById('pagination-viewport');
        var indicatorEl = document.getElementById('page-indicator');

        viewportEl.innerHTML = pages[currentPage];
        indicatorEl.innerHTML = 'Page ' + (currentPage + 1) + ' of ' + totalPages;
        
        // Scroll to top of content, essential for Kindle
        document.body.scrollTop = 0; 
        document.documentElement.scrollTop = 0;
    }

    function handleNextClick(event) {
        if (event.preventDefault) { event.preventDefault(); } else { event.returnValue = false; }

        if (currentPage < totalPages - 1) {
            showPage(currentPage + 1);
        } else {
            var nextChapterUrl = document.getElementById('next-button').getAttribute('href');
            if (nextChapterUrl) {
                window.location.href = nextChapterUrl;
            }
        }
    }

    function handlePrevClick(event) {
        if (event.preventDefault) { event.preventDefault(); } else { event.returnValue = false; }

        if (currentPage > 0) {
            showPage(currentPage - 1);
        } else {
            var prevChapterUrl = document.getElementById('prev-button').getAttribute('href');
            if (prevChapterUrl) {
                window.location.href = prevChapterUrl;
            }
        }
    }
</script>
            `;

            var html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>${cleanTitle}</title>
    <style>
        body {
            font-family: serif; font-size: 1.8rem; line-height: 1.7;
            margin: 0; padding: 2rem; background-color: white; color: black;
            overflow-x: hidden; /* Allow vertical scroll for calculation */
        }
        .inner-content { max-width: 1000px; margin: 0 auto; }
        h1 { font-size: 2.2rem; text-align: left; margin-bottom: 2rem; font-weight: normal; }
        .std-text { font-size: inherit; line-height: inherit; }
        .std-text p { margin-bottom: 1.2rem; }
        .versenum { font-size: 0.8rem; vertical-align: super; color: #666; }
        .crossreference, .footnote { font-size: 0.7rem; vertical-align: super; color: #999; }
        a { color: inherit; text-decoration: none; }
        .navigation-container { display: table; width: 100%; margin-top: 2rem; clear: both; }
        .nav-button { display: table-cell; width: 60px; height: 60px; text-align: center; vertical-align: middle; background: white; color: #666; font-size: 2rem; font-weight: bold; }
        .nav-button:hover { color: #333; }
        .nav-button.disabled { color: #ccc; cursor: default; }
        .footer { display: table-cell; text-align: center; vertical-align: middle; padding: 0 1rem; }
        .footer p { font-size: 1rem; color: #666; margin: 0; }
        #page-indicator { display: block; font-size: 0.9rem; color: #999; margin-bottom: 0.5rem; }
        #pagination-viewport { overflow: hidden; }
        ${generateDisplayCSS()}
    </style>
</head>
<body>
    <div class="inner-content">
        <h1>${cleanTitle}</h1>
        <div id="pagination-viewport"><p>Loading pages...</p></div>
        <div id="source-content" style="display: none;">${contentContainer}</div>
        <div id="navigation-container">
            <a id="prev-button" href="${navigation.prev || '#'}" class="nav-button ${!navigation.prev ? 'disabled' : ''}">&lt;</a>
            <div class="footer">
                <span id="page-indicator"></span>
                <p>${translation.toUpperCase()} - BibleGateway.com</p>
            </div>
            <a id="next-button" href="${navigation.next || '#'}" class="nav-button ${!navigation.next ? 'disabled' : ''}">&gt;</a>
        </div>
    </div>
    ${clientSideScript}
</body>
</html>`;

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response('Internal server error: ' + error.message, { status: 500 });
        }
    }
};