// <PERSON>f<PERSON>e Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)
// Includes client-side pagination for a page-by-page reading experience.

// Display Configuration - Set to true/false to show/hide elements
var showVerseNumbers = false;      // <sup class="versenum">
var showCrossReferences = false;   // <sup class="crossreference">
var showFootnotes = false;         // <sup class="footnote">
var showSectionHeadings = false;   // <h3> section titles

var BIBLE_BOOKS = [
    { name: 'Genesis', chapters: 50 },
    { name: 'Exodus', chapters: 40 },
    { name: '<PERSON><PERSON>', chapters: 27 },
    { name: 'Numbers', chapters: 36 },
    { name: 'Deuteronomy', chapters: 34 },
    { name: '<PERSON>', chapters: 24 },
    { name: 'Judges', chapters: 21 },
    { name: '<PERSON>', chapters: 4 },
    { name: '1 <PERSON>', chapters: 31 },
    { name: '2 <PERSON>', chapters: 24 },
    { name: '1 <PERSON>', chapters: 22 },
    { name: '2 Kings', chapters: 25 },
    { name: '1 Chronicles', chapters: 29 },
    { name: '2 Chronicles', chapters: 36 },
    { name: '<PERSON>', chapters: 10 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', chapters: 13 },
    { name: '<PERSON>', chapters: 10 },
    { name: 'Job', chapters: 42 },
    { name: 'Psalms', chapters: 150 },
    { name: 'Proverbs', chapters: 31 },
    { name: 'Ecclesiastes', chapters: 12 },
    { name: 'Song of Songs', chapters: 8 },
    { name: 'Isaiah', chapters: 66 },
    { name: '<PERSON>', chapters: 52 },
    { name: 'Lamentations', chapters: 5 },
    { name: 'Ezekiel', chapters: 48 },
    { name: 'Daniel', chapters: 12 },
    { name: 'Hosea', chapters: 14 },
    { name: 'Joel', chapters: 3 },
    { name: 'Amos', chapters: 9 },
    { name: 'Obadiah', chapters: 1 },
    { name: 'Jonah', chapters: 4 },
    { name: 'Micah', chapters: 7 },
    { name: 'Nahum', chapters: 3 },
    { name: 'Habakkuk', chapters: 3 },
    { name: 'Zephaniah', chapters: 3 },
    { name: 'Haggai', chapters: 2 },
    { name: 'Zechariah', chapters: 14 },
    { name: 'Malachi', chapters: 4 },
    { name: 'Matthew', chapters: 28 },
    { name: 'Mark', chapters: 16 },
    { name: 'Luke', chapters: 24 },
    { name: 'John', chapters: 21 },
    { name: 'Acts', chapters: 28 },
    { name: 'Romans', chapters: 16 },
    { name: '1 Corinthians', chapters: 16 },
    { name: '2 Corinthians', chapters: 13 },
    { name: 'Galatians', chapters: 6 },
    { name: 'Ephesians', chapters: 6 },
    { name: 'Philippians', chapters: 4 },
    { name: 'Colossians', chapters: 4 },
    { name: '1 Thessalonians', chapters: 5 },
    { name: '2 Thessalonians', chapters: 3 },
    { name: '1 Timothy', chapters: 6 },
    { name: '2 Timothy', chapters: 4 },
    { name: 'Titus', chapters: 3 },
    { name: 'Philemon', chapters: 1 },
    { name: 'Hebrews', chapters: 13 },
    { name: 'James', chapters: 5 },
    { name: '1 Peter', chapters: 5 },
    { name: '2 Peter', chapters: 3 },
    { name: '1 John', chapters: 5 },
    { name: '2 John', chapters: 1 },
    { name: '3 John', chapters: 1 },
    { name: 'Jude', chapters: 1 },
    { name: 'Revelation', chapters: 22 }
];

function parsePassageReference(passageRef) {
    var match = passageRef.match(/^(.+?)\s+(\d+)$/);
    if (match) {
        return {
            book: match[1].trim(),
            chapter: parseInt(match[2])
        };
    }
    return null;
}

function getNavigationUrls(passageRef, translation) {
    var parsed = parsePassageReference(passageRef);
    if (!parsed) return { prev: null, next: null };
    
    var currentBookIndex = -1;
    for (var i = 0; i < BIBLE_BOOKS.length; i++) {
        if (BIBLE_BOOKS[i].name.toLowerCase() === parsed.book.toLowerCase()) {
            currentBookIndex = i;
            break;
        }
    }
    if (currentBookIndex === -1) return { prev: null, next: null };
    
    var currentBook = BIBLE_BOOKS[currentBookIndex];
    var currentChapter = parsed.chapter;
    
    var prevUrl = null;
    var nextUrl = null;
    
    if (currentChapter > 1) {
        prevUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter - 1)) + '&version=' + translation;
    } else if (currentBookIndex > 0) {
        var prevBook = BIBLE_BOOKS[currentBookIndex - 1];
        prevUrl = '/?search=' + encodeURIComponent(prevBook.name + ' ' + prevBook.chapters) + '&version=' + translation;
    }
    
    if (currentChapter < currentBook.chapters) {
        nextUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter + 1)) + '&version=' + translation;
    } else if (currentBookIndex < BIBLE_BOOKS.length - 1) {
        var nextBook = BIBLE_BOOKS[currentBookIndex + 1];
        nextUrl = '/?search=' + encodeURIComponent(nextBook.name + ' 1') + '&version=' + translation;
    }
    
    return { prev: prevUrl, next: nextUrl };
}

function generateDisplayCSS() {
    var css = '';
    if (!showVerseNumbers) css += '        .versenum { display: none; }\n';
    if (!showCrossReferences) css += '        .crossreference { display: none; }\n';
    if (!showFootnotes) css += '        .footnote { display: none; }\n';
    if (!showSectionHeadings) css += '        h3 { display: none; }\n';
    css += '        .chapternum { display: none; }\n';
    css += '        .woj { color: inherit; font-weight: inherit; }\n';
    return css;
}

export default {
    async fetch(request) {
        try {
            var url = new URL(request.url);
            if (url.pathname === '/favicon.ico') {
                return new Response(null, { status: 204 });
            }

            var searchParam = url.searchParams.get('search');
            var versionParam = url.searchParams.get('version');

            if (!searchParam || !versionParam) {
                return new Response('Invalid URL. Use: /?search=John%205&version=NIV', { status: 400 });
            }

            var translation = versionParam.toUpperCase();
            var passageRef = decodeURIComponent(searchParam);
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            var response = await fetch(bibleGatewayUrl, {
                headers: { 'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0' }
            });

            if (!response.ok) {
                return new Response('Failed to fetch passage: ' + response.status, { status: 500 });
            }

            var htmlText = await response.text();
            var stdTextStart = htmlText.search(/<div[^>]*class=['"]std-text['"][^>]*>/);
            var contentContainer = '';
            var contentStart = -1;
            var contentEnd = -1;

            if (stdTextStart !== -1) {
                // Found std-text div - extract it completely with its opening tag
                var openTagMatch = htmlText.substring(stdTextStart).match(/^<div[^>]*class=['"]std-text['"][^>]*>/);
                if (!openTagMatch) {
                    return new Response('Could not parse std-text div tag.', { status: 404 });
                }
                contentStart = stdTextStart + openTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) {
                    return new Response('Could not find complete std-text content.', { status: 404 });
                }
                // Preserve the complete std-text div structure
                contentContainer = htmlText.substring(stdTextStart, contentEnd + 6);
            } else {
                // Fallback: look for version-specific div
                var versionDivPattern = new RegExp('<div[^>]*class="[^"]*version-' + translation + '[^"]*"[^>]*>');
                var versionDivStart = htmlText.search(versionDivPattern);
                if (versionDivStart === -1) {
                    return new Response('Could not find passage content.', { status: 404 });
                }
                var versionOpenTagMatch = htmlText.substring(versionDivStart).match(/^<div[^>]*>/);
                if (!versionOpenTagMatch) {
                    return new Response('Could not parse version div tag.', { status: 404 });
                }
                contentStart = versionDivStart + versionOpenTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) {
                    return new Response('Could not find complete version content.', { status: 404 });
                }
                var rawContent = htmlText.substring(contentStart, contentEnd);
                // Clean up unwanted elements while preserving structure
                rawContent = rawContent.replace(/<h1[^>]*class=['"][^'"]*passage-display[^'"]*['"][^>]*>[\s\S]*?<\/h1>/g, '');
                rawContent = rawContent.replace(/<div[^>]*class=['"][^'"]*dropdowns[^'"]*['"][^>]*>[\s\S]*?<\/div>/g, '');
                rawContent = rawContent.replace(/<div[^>]*class=['"][^'"]*publisher-info-bottom[^'"]*['"][^>]*>[\s\S]*?<\/div>/g, '');
                // Wrap in std-text div to maintain consistency
                contentContainer = '<div class="std-text">' + rawContent + '</div>';
            }

            // Validate that we have actual content
            if (!contentContainer || contentContainer.trim().length < 50) {
                return new Response('No valid content found in passage.', { status: 404 });
            }

            var titleMatch = htmlText.match(/<h1[^>]*class=['"][^'"]*bcv[^'"]*['"][^>]*>([\s\S]*?)<\/h1>/);
            var title = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : '';
            var cleanTitle = title || passageRef;
            if (cleanTitle.indexOf(' - ') !== -1) {
                cleanTitle = cleanTitle.substring(0, cleanTitle.indexOf(' - '));
            }

            var navigation = getNavigationUrls(passageRef, translation);

            var clientSideScript = '<script>' +
'    // ES5-compatible client-side pagination script' +
'    var pages = [];' +
'    var currentPage = 0;' +
'    var totalPages = 0;' +
'    var isInitialized = false;' +
'' +
'    function initializePagination() {' +
'        if (isInitialized) return;' +
'        isInitialized = true;' +
'' +
'        var sourceEl = document.getElementById("source-content");' +
'        var viewportEl = document.getElementById("pagination-viewport");' +
'        var prevButton = document.getElementById("prev-button");' +
'        var nextButton = document.getElementById("next-button");' +
'        var indicatorEl = document.getElementById("page-indicator");' +
'' +
'        if (!sourceEl) {' +
'            if (indicatorEl) indicatorEl.innerHTML = "No content found.";' +
'            return;' +
'        }' +
'' +
'        // Get the actual content div inside source-content' +
'        var contentDiv = sourceEl.getElementsByTagName("div")[0];' +
'        if (!contentDiv) {' +
'            if (indicatorEl) indicatorEl.innerHTML = "No content div found.";' +
'            return;' +
'        }' +
'' +
'        try {' +
'            paginateContent(contentDiv, viewportEl);' +
'            showPage(0);' +
'        } catch (e) {' +
'            if (indicatorEl) indicatorEl.innerHTML = "Pagination failed: " + e.message;' +
'            return;' +
'        }' +
'' +
'        if (prevButton) {' +
'            prevButton.onclick = function(event) { return handlePrevClick(event); };' +
'        }' +
'        if (nextButton) {' +
'            nextButton.onclick = function(event) { return handleNextClick(event); };' +
'        }' +
'    }' +
'' +
'    function paginateContent(sourceElement, viewportElement) {' +
'        // Calculate available height more accurately' +
'        var navEl = document.getElementById("navigation-container");' +
'        var headerEl = document.getElementsByTagName("h1")[0];' +
'        ' +
'        var topOffset = headerEl ? headerEl.offsetTop + headerEl.offsetHeight + 20 : 100;' +
'        var bottomOffset = navEl ? navEl.offsetTop - 20 : window.innerHeight - 100;' +
'        var viewportHeight = bottomOffset - topOffset;' +
'' +
'        // Ensure minimum height' +
'        if (viewportHeight < 200) {' +
'            viewportHeight = Math.max(200, window.innerHeight * 0.6);' +
'        }' +
'' +
'        // Create temporary container with same styling as viewport' +
'        var tempPage = document.createElement("div");' +
'        tempPage.style.position = "absolute";' +
'        tempPage.style.left = "-9999px";' +
'        tempPage.style.top = "0";' +
'        tempPage.style.width = viewportElement.offsetWidth + "px";' +
'        tempPage.style.fontSize = getComputedStyle ? getComputedStyle(viewportElement).fontSize : "1.8rem";' +
'        tempPage.style.lineHeight = getComputedStyle ? getComputedStyle(viewportElement).lineHeight : "1.7";' +
'        tempPage.style.fontFamily = "serif";' +
'        document.body.appendChild(tempPage);' +
'' +
'        // Convert NodeList to array safely for ES5' +
'        var nodes = [];' +
'        var childNodes = sourceElement.childNodes;' +
'        for (var i = 0; i < childNodes.length; i++) {' +
'            if (childNodes[i].nodeType === 1 || (childNodes[i].nodeType === 3 && childNodes[i].nodeValue.trim())) {' +
'                nodes.push(childNodes[i].cloneNode(true));' +
'            }' +
'        }' +
'' +
'        var nodeIndex = 0;' +
'        while (nodeIndex < nodes.length) {' +
'            var currentNode = nodes[nodeIndex];' +
'            tempPage.appendChild(currentNode);' +
'' +
'            if (tempPage.offsetHeight > viewportHeight && tempPage.childNodes.length > 1) {' +
'                // Remove the last node that caused overflow' +
'                tempPage.removeChild(currentNode);' +
'                ' +
'                // Save current page' +
'                if (tempPage.innerHTML.trim()) {' +
'                    pages.push(tempPage.innerHTML);' +
'                }' +
'                ' +
'                // Clear and start new page with the overflow node' +
'                tempPage.innerHTML = "";' +
'                tempPage.appendChild(currentNode);' +
'            }' +
'            nodeIndex++;' +
'        }' +
'' +
'        // Add final page if it has content' +
'        if (tempPage.innerHTML.trim()) {' +
'            pages.push(tempPage.innerHTML);' +
'        }' +
'        ' +
'        document.body.removeChild(tempPage);' +
'        totalPages = pages.length;' +
'        ' +
'        // Fallback: if no pages created, put all content in one page' +
'        if (totalPages === 0) {' +
'            pages.push(sourceElement.innerHTML);' +
'            totalPages = 1;' +
'        }' +
'    }' +
'' +
'    function showPage(pageIndex) {' +
'        if (pageIndex < 0 || pageIndex >= totalPages) return;' +
'        ' +
'        currentPage = pageIndex;' +
'        var viewportEl = document.getElementById("pagination-viewport");' +
'        var indicatorEl = document.getElementById("page-indicator");' +
'' +
'        if (viewportEl) {' +
'            viewportEl.innerHTML = pages[currentPage];' +
'        }' +
'        if (indicatorEl) {' +
'            indicatorEl.innerHTML = "Page " + (currentPage + 1) + " of " + totalPages;' +
'        }' +
'        ' +
'        // Scroll to top - multiple methods for compatibility' +
'        if (window.scrollTo) {' +
'            window.scrollTo(0, 0);' +
'        }' +
'        if (document.body.scrollTop !== undefined) {' +
'            document.body.scrollTop = 0;' +
'        }' +
'        if (document.documentElement.scrollTop !== undefined) {' +
'            document.documentElement.scrollTop = 0;' +
'        }' +
'    }' +
'' +
'    function handleNextClick(event) {' +
'        // Prevent default behavior' +
'        if (event && event.preventDefault) {' +
'            event.preventDefault();' +
'        } else if (event) {' +
'            event.returnValue = false;' +
'        }' +
'' +
'        if (currentPage < totalPages - 1) {' +
'            showPage(currentPage + 1);' +
'            return false;' +
'        } else {' +
'            var nextButton = document.getElementById("next-button");' +
'            var nextChapterUrl = nextButton ? nextButton.getAttribute("href") : null;' +
'            if (nextChapterUrl && nextChapterUrl !== "#") {' +
'                window.location.href = nextChapterUrl;' +
'            }' +
'            return false;' +
'        }' +
'    }' +
'' +
'    function handlePrevClick(event) {' +
'        // Prevent default behavior' +
'        if (event && event.preventDefault) {' +
'            event.preventDefault();' +
'        } else if (event) {' +
'            event.returnValue = false;' +
'        }' +
'' +
'        if (currentPage > 0) {' +
'            showPage(currentPage - 1);' +
'            return false;' +
'        } else {' +
'            var prevButton = document.getElementById("prev-button");' +
'            var prevChapterUrl = prevButton ? prevButton.getAttribute("href") : null;' +
'            if (prevChapterUrl && prevChapterUrl !== "#") {' +
'                window.location.href = prevChapterUrl;' +
'            }' +
'            return false;' +
'        }' +
'    }' +
'' +
'    // Initialize when DOM is ready' +
'    if (document.readyState === "complete") {' +
'        initializePagination();' +
'    } else if (window.addEventListener) {' +
'        window.addEventListener("load", initializePagination, false);' +
'    } else if (window.attachEvent) {' +
'        window.attachEvent("onload", initializePagination);' +
'    } else {' +
'        window.onload = initializePagination;' +
'    }' +
'</script>';

            var html = '<!DOCTYPE html>' +
'<html lang="en">' +
'<head>' +
'    <meta charset="UTF-8">' +
'    <title>' + cleanTitle + '</title>' +
'    <style>' +
'        body {' +
'            font-family: serif;' +
'            font-size: 18px;' +
'            line-height: 1.7;' +
'            margin: 0;' +
'            padding: 20px;' +
'            background-color: white;' +
'            color: black;' +
'            overflow-x: hidden;' +
'        }' +
'        .inner-content {' +
'            max-width: 1000px;' +
'            margin: 0 auto;' +
'        }' +
'        h1 {' +
'            font-size: 22px;' +
'            text-align: left;' +
'            margin-bottom: 20px;' +
'            font-weight: normal;' +
'        }' +
'        .std-text {' +
'            font-size: inherit;' +
'            line-height: inherit;' +
'        }' +
'        .std-text p {' +
'            margin-bottom: 12px;' +
'        }' +
'        .versenum {' +
'            font-size: 14px;' +
'            vertical-align: super;' +
'            color: #666;' +
'        }' +
'        .crossreference, .footnote {' +
'            font-size: 12px;' +
'            vertical-align: super;' +
'            color: #999;' +
'        }' +
'        a {' +
'            color: inherit;' +
'            text-decoration: none;' +
'        }' +
'        #navigation-container {' +
'            width: 100%;' +
'            margin-top: 20px;' +
'            clear: both;' +
'            position: relative;' +
'            height: 80px;' +
'        }' +
'        .nav-button {' +
'            position: absolute;' +
'            top: 10px;' +
'            width: 60px;' +
'            height: 60px;' +
'            text-align: center;' +
'            line-height: 60px;' +
'            background: white;' +
'            color: #666;' +
'            font-size: 32px;' +
'            font-weight: bold;' +
'            border: 1px solid #ccc;' +
'        }' +
'        #prev-button {' +
'            left: 0;' +
'        }' +
'        #next-button {' +
'            right: 0;' +
'        }' +
'        .nav-button:hover {' +
'            color: #333;' +
'        }' +
'        .nav-button.disabled {' +
'            color: #ccc;' +
'            cursor: default;' +
'        }' +
'        .footer {' +
'            position: absolute;' +
'            left: 80px;' +
'            right: 80px;' +
'            top: 10px;' +
'            height: 60px;' +
'            text-align: center;' +
'            padding: 10px;' +
'        }' +
'        .footer p {' +
'            font-size: 16px;' +
'            color: #666;' +
'            margin: 0;' +
'        }' +
'        #page-indicator {' +
'            display: block;' +
'            font-size: 14px;' +
'            color: #999;' +
'            margin-bottom: 5px;' +
'        }' +
'        #pagination-viewport {' +
'            overflow: hidden;' +
'            min-height: 300px;' +
'        }' +
'        #source-content {' +
'            display: none;' +
'        }' +
        generateDisplayCSS() +
'    </style>' +
'</head>' +
'<body>' +
'    <div class="inner-content">' +
'        <h1>' + cleanTitle + '</h1>' +
'        <div id="pagination-viewport"><p>Loading pages...</p></div>' +
'        <div id="source-content">' + contentContainer + '</div>' +
'        <div id="navigation-container">' +
'            <a id="prev-button" href="' + (navigation.prev || '#') + '" class="nav-button ' + (!navigation.prev ? 'disabled' : '') + '">&lt;</a>' +
'            <div class="footer">' +
'                <span id="page-indicator"></span>' +
'                <p>' + translation.toUpperCase() + ' - BibleGateway.com</p>' +
'            </div>' +
'            <a id="next-button" href="' + (navigation.next || '#') + '" class="nav-button ' + (!navigation.next ? 'disabled' : '') + '">&gt;</a>' +
'        </div>' +
'    </div>' +
    clientSideScript +
'</body>' +
'</html>';

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response('Internal server error: ' + error.message, { status: 500 });
        }
    }
};